import { useCallback, useEffect, useMemo, useState } from 'react';
import { useIntl } from 'react-intl';

import {
  Alert,
  Button,
  Form,
  Page,
  Progress,
  SizableText,
  Stack,
  Textarea,
  useForm,
  useFormWatch,
} from '@onekeyhq/components';
import backgroundApiProxy from '@onekeyhq/kit/src/background/instance/backgroundApiProxy';
import { ControlledNetworkSelectorTrigger } from '@onekeyhq/kit/src/components/AccountSelector';
import { DeriveTypeSelectorFormInput } from '@onekeyhq/kit/src/components/AccountSelector/DeriveTypeSelectorTrigger';
import { useAccountSelectorTrigger } from '@onekeyhq/kit/src/components/AccountSelector/hooks/useAccountSelectorTrigger';
import useAppNavigation from '@onekeyhq/kit/src/hooks/useAppNavigation';
import { useUserWalletProfile } from '@onekeyhq/kit/src/hooks/useUserWalletProfile';
import { useDebounce } from '@onekeyhq/kit/src/hooks/useDebounce';
import { toastSuccessWhenImportAddressOrPrivateKey } from '../../../../utils/toastExistingWalletSwitch';
import type { IAccountDeriveTypes } from '@onekeyhq/kit-bg/src/vaults/types';
import { WALLET_TYPE_IMPORTED } from '@onekeyhq/shared/src/consts/dbConsts';
import { ETranslations } from '@onekeyhq/shared/src/locale';
import { defaultLogger } from '@onekeyhq/shared/src/logger/logger';

import { Tutorials } from '../../components/Tutorials';

type IFormValues = {
  networkId?: string;
  privateKeys?: string;
  deriveType?: IAccountDeriveTypes;
};

interface IPrivateKeyValidationResult {
  privateKey: string;
  isValid: boolean;
  error?: string;
}

interface IBatchImportResult {
  success: number;
  failed: number;
  results: Array<{
    privateKey: string;
    success: boolean;
    error?: string;
    accountId?: string;
  }>;
}

export function ImportBatchPrivateKey() {
  const intl = useIntl();
  const navigation = useAppNavigation();
  const { isSoftwareWalletOnlyUser } = useUserWalletProfile();
  
  const {
    activeAccount: { network },
  } = useAccountSelectorTrigger({ num: 0 });

  const form = useForm<IFormValues>({
    defaultValues: {
      networkId: network?.id,
      privateKeys: '',
      deriveType: 'default',
    },
  });

  const { control, setValue } = form;
  const networkIdText = useFormWatch({ control, name: 'networkId' });
  const privateKeysText = useFormWatch({ control, name: 'privateKeys' });
  const deriveType = useFormWatch({ control, name: 'deriveType' });
  const privateKeysTextDebounced = useDebounce(privateKeysText?.trim() || '', 600);

  const [validationResults, setValidationResults] = useState<IPrivateKeyValidationResult[]>([]);
  const [isValidating, setIsValidating] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [importResults, setImportResults] = useState<IBatchImportResult | null>(null);

  // 解析私钥列表
  const privateKeysList = useMemo(() => {
    if (!privateKeysTextDebounced) return [];
    return privateKeysTextDebounced
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0);
  }, [privateKeysTextDebounced]);

  // 验证私钥
  const validatePrivateKeys = useCallback(async () => {
    if (privateKeysList.length === 0 || !networkIdText) {
      setValidationResults([]);
      return;
    }

    setIsValidating(true);
    const results: IPrivateKeyValidationResult[] = [];

    for (const privateKey of privateKeysList) {
      try {
        const encodedPrivateKey = await backgroundApiProxy.servicePassword.encodeSensitiveText({
          text: privateKey,
        });
        
        const validation = await backgroundApiProxy.serviceAccount.validateGeneralInputOfImporting({
          input: encodedPrivateKey,
          networkId: networkIdText,
          validatePrivateKey: true,
          validateAddress: false,
          validateXpub: false,
          validateXprvt: false,
        });

        if (validation.isValid) {
          results.push({
            privateKey,
            isValid: true,
          });
        } else {
          results.push({
            privateKey,
            isValid: false,
            error: '无效的私钥格式',
          });
        }
      } catch (error) {
        results.push({
          privateKey,
          isValid: false,
          error: (error as Error)?.message || '验证失败',
        });
      }
    }

    setValidationResults(results);
    setIsValidating(false);
  }, [privateKeysList, networkIdText]);

  useEffect(() => {
    void validatePrivateKeys();
  }, [validatePrivateKeys]);

  // 重置派生类型当网络改变时
  useEffect(() => {
    if (networkIdText) {
      setValue('deriveType', 'default');
    }
  }, [networkIdText, setValue]);

  // 批量导入私钥
  const handleBatchImport = useCallback(async () => {
    if (!networkIdText || validationResults.length === 0) return;

    const validPrivateKeys = validationResults.filter(result => result.isValid);
    if (validPrivateKeys.length === 0) return;

    setIsImporting(true);
    setImportProgress(0);
    setImportResults(null);

    const results: IBatchImportResult = {
      success: 0,
      failed: 0,
      results: [],
    };

    for (let i = 0; i < validPrivateKeys.length; i++) {
      const { privateKey } = validPrivateKeys[i];
      
      try {
        const encodedPrivateKey = await backgroundApiProxy.servicePassword.encodeSensitiveText({
          text: privateKey,
        });

        const importResult = await backgroundApiProxy.serviceAccount.addImportedAccount({
          input: encodedPrivateKey,
          deriveType,
          networkId: networkIdText,
          shouldCheckDuplicateName: true,
        });

        const accountId = importResult?.accounts?.[0]?.id;

        if (accountId) {
          toastSuccessWhenImportAddressOrPrivateKey({
            isOverrideAccounts: importResult?.isOverrideAccounts,
            accountId,
          });

          results.results.push({
            privateKey,
            success: true,
            accountId,
          });
          results.success++;
        } else {
          results.results.push({
            privateKey,
            success: false,
            error: '导入失败：未能创建账户',
          });
          results.failed++;
        }
      } catch (error) {
        results.results.push({
          privateKey,
          success: false,
          error: (error as Error)?.message || '导入失败',
        });
        results.failed++;
      }

      setImportProgress(((i + 1) / validPrivateKeys.length) * 100);
    }

    setImportResults(results);
    setIsImporting(false);

    // 记录日志
    defaultLogger.account.wallet.walletAdded({
      status: 'success',
      addMethod: 'ImportWallet',
      details: {
        importType: 'batchPrivateKey',
        count: results.success,
      },
      isSoftwareWalletOnlyUser,
    });

    // 如果有成功导入的账户，返回上一页
    if (results.success > 0) {
      setTimeout(() => {
        navigation.popStack();
      }, 2000);
    }
  }, [networkIdText, validationResults, deriveType, navigation, isSoftwareWalletOnlyUser]);

  const validCount = validationResults.filter(result => result.isValid).length;
  const invalidCount = validationResults.filter(result => !result.isValid).length;
  const isFormValid = validCount > 0 && networkIdText;

  return (
    <Page scrollEnabled>
      <Page.Header
        title={intl.formatMessage({
          id: ETranslations.global_batch_import_private_key,
        })}
      />
      <Page.Body px="$5">
        <Form form={form}>
          <Form.Field
            label={intl.formatMessage({ id: ETranslations.global_network })}
            name="networkId"
          >
            <ControlledNetworkSelectorTrigger />
          </Form.Field>

          <DeriveTypeSelectorFormInput
            networkId={networkIdText}
            name="deriveType"
          />
          
          <Form.Field
            label={intl.formatMessage({ id: ETranslations.global_private_keys })}
            name="privateKeys"
          >
            <Textarea
              placeholder={intl.formatMessage({
                id: ETranslations.global_batch_import_private_key_placeholder,
              })}
              minHeight="$32"
              maxHeight="$48"
              secureTextEntry
            />
          </Form.Field>
        </Form>

        {privateKeysList.length > 0 && (
          <Stack mt="$4" space="$2">
            <SizableText size="$bodyMd" color="$textSubdued">
              {intl.formatMessage(
                { id: ETranslations.global_validation_status },
                {
                  valid: validCount,
                  invalid: invalidCount,
                  total: privateKeysList.length,
                }
              )}
            </SizableText>
            
            {isValidating && (
              <SizableText size="$bodySm" color="$textSubdued">
                {intl.formatMessage({ id: ETranslations.global_validating })}
              </SizableText>
            )}
          </Stack>
        )}

        {invalidCount > 0 && (
          <Alert
            type="critical"
            title={intl.formatMessage({ id: ETranslations.global_invalid_private_keys_found })}
            description={intl.formatMessage({
              id: ETranslations.global_please_check_and_correct_invalid_private_keys,
            })}
            mt="$4"
          />
        )}

        {isImporting && (
          <Stack mt="$4" space="$2">
            <SizableText size="$bodyMd">
              {intl.formatMessage({ id: ETranslations.global_importing })}
            </SizableText>
            <Progress value={importProgress} />
          </Stack>
        )}

        {importResults && (
          <Alert
            type={importResults.failed === 0 ? 'success' : 'warning'}
            title={intl.formatMessage(
              { id: ETranslations.global_import_completed },
              {
                success: importResults.success,
                failed: importResults.failed,
              }
            )}
            mt="$4"
          />
        )}

        <Tutorials
          mt="$6"
          list={[
            {
              title: intl.formatMessage({ id: ETranslations.faq_private_key }),
              description: intl.formatMessage({
                id: ETranslations.faq_private_key_desc,
              }),
            },
            {
              title: intl.formatMessage({
                id: ETranslations.faq_batch_import_tips,
              }),
              description: intl.formatMessage({
                id: ETranslations.faq_batch_import_tips_desc,
              }),
            },
          ]}
        />
      </Page.Body>
      
      <Page.Footer>
        <Button
          variant="primary"
          size="large"
          disabled={!isFormValid || isValidating || isImporting}
          loading={isImporting}
          onPress={handleBatchImport}
        >
          {intl.formatMessage(
            { id: ETranslations.global_import_count_accounts },
            { count: validCount }
          )}
        </Button>
      </Page.Footer>
    </Page>
  );
}

export default ImportBatchPrivateKey;
