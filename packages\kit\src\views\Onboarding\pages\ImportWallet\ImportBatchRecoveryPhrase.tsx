import { useCallback, useEffect, useMemo, useState } from 'react';
import { useIntl } from 'react-intl';

import {
  Alert,
  Button,
  Form,
  Page,
  Progress,
  SizableText,
  Stack,
  Textarea,
  useForm,
  useFormWatch,
} from '@onekeyhq/components';
import { EMnemonicType } from '@onekeyhq/core/src/secret';
import backgroundApiProxy from '@onekeyhq/kit/src/background/instance/backgroundApiProxy';
import { ControlledNetworkSelectorTrigger } from '@onekeyhq/kit/src/components/AccountSelector';
import { useAccountSelectorTrigger } from '@onekeyhq/kit/src/components/AccountSelector/hooks/useAccountSelectorTrigger';
import useAppNavigation from '@onekeyhq/kit/src/hooks/useAppNavigation';
import { useUserWalletProfile } from '@onekeyhq/kit/src/hooks/useUserWalletProfile';
import { useDebounce } from '@onekeyhq/kit/src/hooks/useDebounce';
import { ETranslations } from '@onekeyhq/shared/src/locale';
import { defaultLogger } from '@onekeyhq/shared/src/logger/logger';
import { EOnboardingPages } from '@onekeyhq/shared/src/routes';

import { Tutorials } from '../../components/Tutorials';

type IFormValues = {
  networkId?: string;
  mnemonics?: string;
};

interface IMnemonicValidationResult {
  mnemonic: string;
  isValid: boolean;
  error?: string;
  mnemonicType?: EMnemonicType;
}

interface IBatchImportResult {
  success: number;
  failed: number;
  results: Array<{
    mnemonic: string;
    success: boolean;
    error?: string;
    accountId?: string;
  }>;
}

export function ImportBatchRecoveryPhrase() {
  const intl = useIntl();
  const navigation = useAppNavigation();
  const { isSoftwareWalletOnlyUser } = useUserWalletProfile();
  
  const {
    activeAccount: { network },
  } = useAccountSelectorTrigger({ num: 0 });

  const form = useForm<IFormValues>({
    defaultValues: {
      networkId: network?.id,
      mnemonics: '',
    },
  });

  const { control, setValue } = form;
  const networkIdText = useFormWatch({ control, name: 'networkId' });
  const mnemonicsText = useFormWatch({ control, name: 'mnemonics' });
  const mnemonicsTextDebounced = useDebounce(mnemonicsText?.trim() || '', 600);

  const [validationResults, setValidationResults] = useState<IMnemonicValidationResult[]>([]);
  const [isValidating, setIsValidating] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [importResults, setImportResults] = useState<IBatchImportResult | null>(null);

  // 解析助记词列表
  const mnemonicsList = useMemo(() => {
    if (!mnemonicsTextDebounced) return [];
    return mnemonicsTextDebounced
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0);
  }, [mnemonicsTextDebounced]);

  // 验证助记词
  const validateMnemonics = useCallback(async () => {
    if (mnemonicsList.length === 0) {
      setValidationResults([]);
      return;
    }

    setIsValidating(true);
    const results: IMnemonicValidationResult[] = [];

    for (const mnemonic of mnemonicsList) {
      try {
        const encodedMnemonic = await backgroundApiProxy.servicePassword.encodeSensitiveText({
          text: mnemonic,
        });
        const validation = await backgroundApiProxy.serviceAccount.validateMnemonic(encodedMnemonic);
        results.push({
          mnemonic,
          isValid: true,
          mnemonicType: validation.mnemonicType,
        });
      } catch (error) {
        results.push({
          mnemonic,
          isValid: false,
          error: (error as Error)?.message || '无效的助记词',
        });
      }
    }

    setValidationResults(results);
    setIsValidating(false);
  }, [mnemonicsList]);

  useEffect(() => {
    void validateMnemonics();
  }, [validateMnemonics]);

  // 批量导入助记词
  const handleBatchImport = useCallback(async () => {
    if (!networkIdText || validationResults.length === 0) return;

    const validMnemonics = validationResults.filter(result => result.isValid);
    if (validMnemonics.length === 0) return;

    setIsImporting(true);
    setImportProgress(0);
    setImportResults(null);

    const results: IBatchImportResult = {
      success: 0,
      failed: 0,
      results: [],
    };

    for (let i = 0; i < validMnemonics.length; i++) {
      const { mnemonic, mnemonicType } = validMnemonics[i];
      
      try {
        // 对于TON助记词，需要特殊处理
        if (mnemonicType === EMnemonicType.TON) {
          // TON助记词导入逻辑
          const encodedMnemonic = await backgroundApiProxy.servicePassword.encodeSensitiveText({
            text: mnemonic,
          });
          // 这里需要调用TON特定的导入逻辑
          // 暂时跳过TON助记词的批量导入
          results.results.push({
            mnemonic,
            success: false,
            error: 'TON助记词暂不支持批量导入',
          });
          results.failed++;
        } else {
          // 普通BIP39助记词导入
          navigation.push(EOnboardingPages.FinalizeWalletSetup, {
            mnemonic,
            mnemonicType,
            isWalletBackedUp: true,
          });
          
          results.results.push({
            mnemonic,
            success: true,
          });
          results.success++;
        }
      } catch (error) {
        results.results.push({
          mnemonic,
          success: false,
          error: (error as Error)?.message || '导入失败',
        });
        results.failed++;
      }

      setImportProgress(((i + 1) / validMnemonics.length) * 100);
    }

    setImportResults(results);
    setIsImporting(false);

    // 记录日志
    defaultLogger.account.wallet.walletAdded({
      status: 'success',
      addMethod: 'ImportWallet',
      details: {
        importType: 'batchRecoveryPhrase',
        count: results.success,
      },
      isSoftwareWalletOnlyUser,
    });

    // 如果有成功导入的钱包，返回上一页
    if (results.success > 0) {
      setTimeout(() => {
        navigation.popStack();
      }, 2000);
    }
  }, [networkIdText, validationResults, navigation, isSoftwareWalletOnlyUser]);

  const validCount = validationResults.filter(result => result.isValid).length;
  const invalidCount = validationResults.filter(result => !result.isValid).length;
  const isFormValid = validCount > 0 && networkIdText;

  return (
    <Page scrollEnabled>
      <Page.Header
        title={intl.formatMessage({
          id: ETranslations.global_batch_import_recovery_phrase,
        })}
      />
      <Page.Body px="$5">
        <Form form={form}>
          <Form.Field
            label={intl.formatMessage({ id: ETranslations.global_network })}
            name="networkId"
          >
            <ControlledNetworkSelectorTrigger />
          </Form.Field>
          
          <Form.Field
            label={intl.formatMessage({ id: ETranslations.global_recovery_phrases })}
            name="mnemonics"
          >
            <Textarea
              placeholder={intl.formatMessage({
                id: ETranslations.global_batch_import_recovery_phrase_placeholder,
              })}
              minHeight="$32"
              maxHeight="$48"
            />
          </Form.Field>
        </Form>

        {mnemonicsList.length > 0 && (
          <Stack mt="$4" space="$2">
            <SizableText size="$bodyMd" color="$textSubdued">
              {intl.formatMessage(
                { id: ETranslations.global_validation_status },
                {
                  valid: validCount,
                  invalid: invalidCount,
                  total: mnemonicsList.length,
                }
              )}
            </SizableText>
            
            {isValidating && (
              <SizableText size="$bodySm" color="$textSubdued">
                {intl.formatMessage({ id: ETranslations.global_validating })}
              </SizableText>
            )}
          </Stack>
        )}

        {invalidCount > 0 && (
          <Alert
            type="critical"
            title={intl.formatMessage({ id: ETranslations.global_invalid_mnemonics_found })}
            description={intl.formatMessage({
              id: ETranslations.global_please_check_and_correct_invalid_mnemonics,
            })}
            mt="$4"
          />
        )}

        {isImporting && (
          <Stack mt="$4" space="$2">
            <SizableText size="$bodyMd">
              {intl.formatMessage({ id: ETranslations.global_importing })}
            </SizableText>
            <Progress value={importProgress} />
          </Stack>
        )}

        {importResults && (
          <Alert
            type={importResults.failed === 0 ? 'success' : 'warning'}
            title={intl.formatMessage(
              { id: ETranslations.global_import_completed },
              {
                success: importResults.success,
                failed: importResults.failed,
              }
            )}
            mt="$4"
          />
        )}

        <Tutorials
          mt="$6"
          list={[
            {
              title: intl.formatMessage({
                id: ETranslations.faq_recovery_phrase,
              }),
              description: intl.formatMessage({
                id: ETranslations.faq_recovery_phrase_explaination,
              }),
            },
            {
              title: intl.formatMessage({
                id: ETranslations.faq_batch_import_tips,
              }),
              description: intl.formatMessage({
                id: ETranslations.faq_batch_import_tips_desc,
              }),
            },
          ]}
        />
      </Page.Body>
      
      <Page.Footer>
        <Button
          variant="primary"
          size="large"
          disabled={!isFormValid || isValidating || isImporting}
          loading={isImporting}
          onPress={handleBatchImport}
        >
          {intl.formatMessage(
            { id: ETranslations.global_import_count_wallets },
            { count: validCount }
          )}
        </Button>
      </Page.Footer>
    </Page>
  );
}

export default ImportBatchRecoveryPhrase;
